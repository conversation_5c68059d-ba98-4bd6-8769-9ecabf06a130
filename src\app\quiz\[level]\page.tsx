import { notFound } from 'next/navigation';
import { generateQuestionWithGemini } from '@/lib/gemini';
import { Level, Language, Question } from '@/types';
import { questionStore } from '@/lib/questionStore';
import QuizClient from '@/components/QuizClient';
import ErrorPage from '@/components/ErrorPage';

interface QuizPageProps {
  params: Promise<{
    level: string;
  }>;
  searchParams: Promise<{
    lang?: string;
  }>;
}

// Validate level parameter
function isValidLevel(level: string): level is Level {
  return ['beginner', 'intermediate', 'advanced'].includes(level);
}

// Validate language parameter
function isValidLanguage(lang: string): lang is Language {
  return ['en', 'vi', 'zh'].includes(lang);
}

export default async function QuizPage({ params, searchParams }: QuizPageProps) {
  const { level } = await params;
  const resolvedSearchParams = await searchParams;
  const language = resolvedSearchParams.lang || 'vi';

  // Validate parameters
  if (!isValidLevel(level)) {
    notFound();
  }

  if (!isValidLanguage(language)) {
    notFound();
  }

  try {
    // Generate question server-side using Gemini AI
    const fullQuestion = await generateQuestionWithGemini(level, language);

    // Store the answer and explanation server-side
    await questionStore.set(fullQuestion.id, {
      correctAnswer: fullQuestion.correctAnswer,
      explanation: fullQuestion.explanation,
      level: fullQuestion.level,
      language: language
    });

    // Return only question data without answer for client
    const safeQuestion: Question = {
      id: fullQuestion.id,
      question: fullQuestion.question,
      options: fullQuestion.options,
      level: fullQuestion.level
    };

    return (
      <QuizClient
        initialQuestion={safeQuestion}
        level={level}
        language={language}
      />
    );
  } catch (error) {
    console.error('Error generating question:', error);

    // Return error page or fallback
    return <ErrorPage />;
  }
}

// Generate metadata for SEO
export async function generateMetadata({ params }: QuizPageProps) {
  const { level } = await params;
  
  if (!isValidLevel(level)) {
    return {
      title: 'Quiz Not Found',
    };
  }

  return {
    title: `${level.charAt(0).toUpperCase() + level.slice(1)} English Quiz`,
    description: `Test your English skills with our ${level} level quiz. Improve your English learning with AI-generated questions.`,
  };
}

// Generate static params for better performance (optional)
export async function generateStaticParams() {
  return [
    { level: 'beginner' },
    { level: 'intermediate' },
    { level: 'advanced' },
  ];
}
